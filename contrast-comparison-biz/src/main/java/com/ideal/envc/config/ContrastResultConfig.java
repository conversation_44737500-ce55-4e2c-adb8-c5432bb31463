package com.ideal.envc.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.io.Serializable;

/**
 * 一致性比对结果配置类
 * 用于配置比对结果文件的存储路径等相关参数
 *
 * <AUTHOR>
 */
@RefreshScope
@Component
@ConfigurationProperties(prefix = "contrast.result")
public class ContrastResultConfig implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /**
     * 模型配置
     */
    private Model model;

    /**
     * 获取模型配置
     *
     * @return 模型配置
     */
    public Model getModel() {
        return model;
    }

    /**
     * 设置模型配置
     *
     * @param model 模型配置
     */
    public void setModel(Model model) {
        this.model = model;
    }

    /**
     * 获取是否开启结果存储模式为file
     * 便捷方法，直接获取文件存储功能的启用状态
     *
     * @return true-开启文件存储模式，false-未开启或配置不完整
     */
    public boolean isFileStorageEnabled() {
        try {
            return this.model != null
                    && this.model.getFile() != null
                    && Boolean.TRUE.equals(this.model.getFile().getEnabled());
        } catch (Exception e) {
            /* 配置异常时返回false，确保系统稳定性 */
            return false;
        }
    }

    /**
     * 获取结果存储模式为file的基础目录
     * 便捷方法，直接获取文件存储的基础路径
     *
     * @return 基础目录路径，如果配置不完整则返回默认路径 "/opt"
     */
    public String getFileStorageBasePath() {
        try {
            if (this.model != null
                    && this.model.getFile() != null
                    && this.model.getFile().getBasePath() != null
                    && !this.model.getFile().getBasePath().trim().isEmpty()) {
                return this.model.getFile().getBasePath().trim();
            }
        } catch (Exception e) {
            /* 配置异常时返回默认路径，确保系统稳定性 */
        }

        /* 返回默认基础路径 */
        return "";
    }

    /**
     * 模型配置类
     */
    public static class Model implements Serializable {
        
        private static final long serialVersionUID = 1L;

        /**
         * 文件配置
         */
        private File file;

        /**
         * 获取文件配置
         *
         * @return 文件配置
         */
        public File getFile() {
            return file;
        }

        /**
         * 设置文件配置
         *
         * @param file 文件配置
         */
        public void setFile(File file) {
            this.file = file;
        }
    }

    /**
     * 文件配置类
     */
    public static class File implements Serializable {
        
        private static final long serialVersionUID = 1L;

        /**
         * 是否启用文件存储，默认为false
         */
        private Boolean enabled = Boolean.FALSE;

        /**
         * 文件存储基础路径，默认为 空
         */
        private String basePath = "";

        /**
         * 获取是否启用
         *
         * @return 是否启用
         */
        public Boolean getEnabled() {
            return enabled;
        }

        /**
         * 设置是否启用
         *
         * @param enabled 是否启用
         */
        public void setEnabled(Boolean enabled) {
            this.enabled = enabled;
        }

        /**
         * 获取基础路径
         *
         * @return 基础路径
         */
        public String getBasePath() {
            return basePath;
        }

        /**
         * 设置基础路径
         *
         * @param basePath 基础路径
         */
        public void setBasePath(String basePath) {
            this.basePath = basePath;
        }
    }
}
