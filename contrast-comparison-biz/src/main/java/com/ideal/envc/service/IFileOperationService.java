package com.ideal.envc.service;

import java.util.Collection;

/**
 * 文件操作服务接口
 * 提供基于配置的文件操作功能
 *
 * <AUTHOR>
 */
public interface IFileOperationService {

    /**
     * 生成比对结果文件路径
     * 基于配置的基础路径和流程ID生成文件路径
     *
     * @param flowId 流程ID
     * @return 文件绝对路径（含文件名）
     */
    String generateResultFilePath(String flowId);

    /**
     * 保存比对结果文件
     * 将比对结果内容保存到指定的文件中
     *
     * @param content 文件内容
     * @param flowId  流程ID
     * @return true-保存成功，false-保存失败
     */
    boolean saveResultFile(String content, String flowId);

    /**
     * 保存比对结果文件到指定路径
     * 将比对结果内容保存到指定的文件路径中
     *
     * @param content  文件内容
     * @param filePath 文件绝对路径（含文件名）
     * @return true-保存成功，false-保存失败
     */
    boolean saveResultFileOfPath(String content, String filePath);

    /**
     * 读取比对结果文件
     * 从指定路径读取比对结果文件内容
     *
     * @param filePath 文件绝对路径（含文件名）
     * @return 文件内容字符串，读取失败返回null
     */
    String readResultFile(String filePath);

    /**
     * 更新比对结果文件
     * 更新指定路径的比对结果文件内容
     *
     * @param content     文件内容
     * @param filePath    文件绝对路径（含文件名）
     * @param isReplace   是否替换原有内容，true-替换，false-追加
     * @return true-更新成功，false-更新失败
     */
    boolean updateResultFile(String content, String filePath, boolean isReplace);

    /**
     * 更新比对结果文件（默认替换模式）
     * 更新指定路径的比对结果文件内容，默认替换原有内容
     *
     * @param content  文件内容
     * @param filePath 文件绝对路径（含文件名）
     * @return true-更新成功，false-更新失败
     */
    boolean updateResultFile(String content, String filePath);

    /**
     * 删除比对结果文件
     * 删除指定路径的比对结果文件
     *
     * @param filePath 文件绝对路径（含文件名）
     * @return true-删除成功，false-删除失败
     */
    boolean deleteResultFile(String filePath);

    /**
     * 批量删除比对结果文件
     * 批量删除多个比对结果文件
     *
     * @param filePaths 文件绝对路径集合
     * @return true-全部删除成功，false-存在删除失败的文件
     */
    boolean batchDeleteResultFiles(Collection<String> filePaths);


}
